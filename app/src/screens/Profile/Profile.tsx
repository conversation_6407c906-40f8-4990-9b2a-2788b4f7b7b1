import { useWallet, WalletScreenNames } from '@bp/bppay-wallet-feature';
import {
  ProfileScreenNames,
  ProfileScreensContextProvider,
  Screens,
  useAppSettings,
  useLanguage,
  UserContact,
} from '@bp/profile-mfe';
import { UserTypes } from '@bp/profile-mfe/dist/common/enums';
import {
  CipStartJourneyFlow,
  SFConsentUpdate,
  UpdateUserIdentityResponse,
  useAuth,
} from '@bp/pulse-auth-sdk';
import { PartnerType } from '@bp/pulse-shared-types/src/enums/PartnerType';
import { SupportedCountries } from '@common/enums';
import { ScreenWrapper, SoftOnboardingBanner } from '@components';
import { LoadingSpinner } from '@components/LoadingSpinner/LoadingSpinner';
import env from '@env';
import { useConfig } from '@providers/ConfigProvider';
import { useReturnNavigation } from '@providers/ReturnNavigationProvider';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { RouteProp, useRoute } from '@react-navigation/native';
import { navigate, navigation } from '@utils/navigation';
import { getMappedConsents } from '@utils/profileHelper';
import React, { useCallback, useEffect, useState } from 'react';

export type ProfileScreenRouteParams = RouteProp<
  {
    Profile: {
      path: string;
    };
  },
  'Profile'
>;

const Render = () => {
  const {
    profile_mfe: featureFlags,
    external_links: externalLinks,
    app_shell: { enableSoftOnboarding },
    subscription_mfe: subscriptionFeatureFlags,
  } = useConfig();
  const { userInfo } = useAppSettings();

  const { returnParams, returnNavigate } = useReturnNavigation();
  const { refetchUserInfo } = useAppSettings();

  const {
    consents,
    loading,
    updateConsent,
    loginOrRegister,
    logout,
    getConsents,
    authenticated,
    updateContact,
    updatePhone,
    getUser,
    updateEmail,
    verifyPhone,
  } = useAuth();
  const { language } = useLanguage();
  const { selectedCard, triggerAddPaymentCardFlow } = useWallet();
  const userType = userInfo?.userType as UserTypes.PAYG_WALLET | UserTypes.PAYG;
  const migrationStatusConst = userInfo?.migrationUserStatus;
  const { params } = useRoute<ProfileScreenRouteParams>();

  const [authActive, setAuthActive] = useState(false);
  const [location, setLocation] = useState<string>('');
  const [isSubscriptionEnabled, setIsSubscriptionEnabled] = useState(
    featureFlags.subscription,
  );

  const PERMISSION_STATUS_DENIED_TEMPORARILY =
    'GeoLocationPermissionStatus.DENIED_TEMPORARILY';
  const LOCATION_PERMISSIONS_STATUS = 'MapMfe.LocationPermissionsStatus';

  useEffect(() => {
    const fetchLocation = async () => {
      try {
        const storedLocation = await AsyncStorage.getItem(
          LOCATION_PERMISSIONS_STATUS,
        );
        storedLocation?.trim() && setLocation(storedLocation.trim());
      } catch (error) {
        setLocation(PERMISSION_STATUS_DENIED_TEMPORARILY);
        console.error('Error fetching location:', error);
      }
    };

    fetchLocation();
  }, []);

  useEffect(() => {
    // Determine if subscription should be enabled based on migration status and feature flag
    const shouldEnableSubscription =
      userType !== UserTypes.PAYG ||
      !migrationStatusConst ||
      !subscriptionFeatureFlags?.block_subscription_upgrade_during_migration;

    setIsSubscriptionEnabled(
      shouldEnableSubscription && featureFlags.subscription,
    );
  }, [
    userType,
    migrationStatusConst,
    subscriptionFeatureFlags,
    featureFlags.subscription,
  ]);

  const refetchConsents = useCallback(async () => {
    if (authenticated) {
      return await getConsents();
    } else {
      return undefined;
    }
  }, [authenticated, getConsents]);

  const uberproLoginRedirect = useCallback(async () => {
    await logout();
    await loginOrRegister(language);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [language]);

  const handleRegistration = useCallback(async () => {
    if (params?.path?.includes('registration')) {
      setAuthActive(true);
      try {
        await loginOrRegister(language, CipStartJourneyFlow.REGISTRATION);
      } catch {
        setAuthActive(false);
        navigate('Error', {}, false);
      }
    }
  }, [params?.path, language, loginOrRegister]);

  useEffect(() => {
    if (params?.path?.includes('uberpro_login')) {
      uberproLoginRedirect();
    }
  }, [params?.path, uberproLoginRedirect]);

  useEffect(() => {
    handleRegistration();
  }, [handleRegistration]);

  useEffect(() => {
    if (
      params?.path?.includes('profile_marketing_preferences') &&
      !authenticated
    ) {
      navigate(ProfileScreenNames.Profile, {}, true);
    }
    if (
      params?.path?.includes('email_address_successfully_added') &&
      authenticated
    ) {
      navigate(
        'Tabs',
        {
          screen: 'Profile',
          params: {
            screen: 'ProfileMFE.Profile',
            params: { isEmailVerified: true },
          },
        },
        false,
      );
    }
  }, [params?.path, authenticated]);

  // Map IDP consents to Profile consents object
  const mappedConsents = getMappedConsents(consents);

  // Navigate to registration screen from gopasswordless widget
  const onGoPasswordless = useCallback(() => navigate('Registration', {}), []);
  // Navigate to wallet screen
  const onNavigateWallet = useCallback(
    () => navigate(WalletScreenNames.HostRoot, {}),
    [],
  );
  // Navigate to charge activity screen
  const onNavigateChargeHistory = useCallback(
    () => navigate('ChargeActivity', {}),
    [],
  );
  // Navigate to RFID screen
  const onNavigateRFID = useCallback(
    () =>
      navigate('RFID', {
        screen: 'MembershipCard',
        params: { userJourney: 'profile' },
      }),
    [],
  );

  // Navigate to credit screen
  const onNavigateCredit = useCallback(() => navigate('Credit', {}), []);

  // Navigate to map
  const onNavigateMap = useCallback(
    () => navigate('Tabs', { screen: 'Map' }, true),
    [],
  );

  // Navigate to offers
  const onNavigateOffers = useCallback(() => navigate('Offers'), []);

  const onNavigatePartnerDriver = useCallback(() => {
    refetchUserInfo();
    navigate('PartnerDriver', { userJourney: 'partner_unlinking' });
  }, [refetchUserInfo]);

  // Navigate to subscription screen
  const onNavigateSubscription = useCallback(() => {
    if (
      userInfo?.partnerType === PartnerType.ADAC &&
      userInfo?.userCountry === SupportedCountries.DE
    ) {
      return navigate('PartnerDriver', {
        screen: 'LoadingOverlay',
        params: { isFromSubscription: true },
      });
    } else {
      return navigate('Subscription', {});
    }
  }, [userInfo?.partnerType, userInfo?.userCountry]);
  // Navigate to delete account screen
  const onNavigateRTBF = useCallback(() => navigate('RTBF', {}), []);

  const onUpdateConsent = async (userConsents: SFConsentUpdate[]) => {
    return updateConsent(userConsents);
  };

  // Stub method to be implemented once editInfo feature is ready
  const onUpdateContact = (userContact: UserContact) => {
    return updateContact(userContact);
  };

  const onUpdatePhone = (
    phoneNumber: string,
  ): Promise<UpdateUserIdentityResponse | undefined> => {
    return updatePhone(phoneNumber, language, env.BRAND);
  };

  const onVerifyPhone = (
    sessionId: string,
    otp: string,
  ): Promise<UpdateUserIdentityResponse | undefined> => {
    return verifyPhone(sessionId, otp);
  };

  const onUpdateEmail = (
    email: string,
  ): Promise<UpdateUserIdentityResponse | undefined> => {
    return updateEmail(email, language, env.BRAND);
  };

  const refetchUserContact = useCallback(async () => {
    if (authenticated) {
      await getUser();
    }
  }, [authenticated, getUser]);

  const onAuth = async (startJourney: CipStartJourneyFlow) => {
    setAuthActive(true);
    try {
      await loginOrRegister(language, startJourney);
    } catch {
      setAuthActive(false);
      navigate('Error', {}, false);
    }
    setAuthActive(false);
  };

  const onLogin = async () => {
    onAuth(CipStartJourneyFlow.LOGIN);
  };

  const onRegister = async () => {
    onAuth(CipStartJourneyFlow.REGISTRATION);
  };

  if (authActive || (!authenticated && loading)) {
    return <LoadingSpinner />;
  }

  function onNavigateCancel(): void {
    const screen = returnParams?.screen?.split('MFE')?.[0] || 'Map';

    navigate(
      'Tabs',
      { screen, params: { screen: returnParams?.screen } },
      true,
    );
  }

  const onAddressCompleted = () => {
    if (returnParams) {
      returnNavigate();
    } else {
      navigate(
        'Tabs',
        {
          screen: 'Profile',
          params: { screen: ProfileScreenNames.UserDetails },
        },
        true,
      );
    }
  };

  return (
    <ScreenWrapper>
      <ProfileScreensContextProvider
        loading={loading}
        consents={mappedConsents}
        locale={language}
        mapsApiKey={env.G_MAPS_KEY}
        featureFlags={{
          ...featureFlags,
          subscription: isSubscriptionEnabled,
        }}
        externalLinks={externalLinks}
        navigation={navigation}
        onLogin={onLogin}
        onLogout={logout}
        onRegister={onRegister}
        onNavigateWallet={onNavigateWallet}
        onNavigateChargeHistory={onNavigateChargeHistory}
        onNavigateRFID={onNavigateRFID}
        onNavigateCredit={onNavigateCredit}
        onNavigateSubscription={onNavigateSubscription}
        onNavigateRTBF={onNavigateRTBF}
        onNavigateMap={onNavigateMap}
        onNavigateOffers={onNavigateOffers}
        onNavigateCancel={onNavigateCancel}
        onNavigatePartnerDriver={onNavigatePartnerDriver}
        onAddressCompleted={onAddressCompleted}
        onGoPasswordless={onGoPasswordless}
        onUpdateConsent={onUpdateConsent}
        onUpdateContact={onUpdateContact}
        onUpdatePhone={onUpdatePhone}
        onVerifyPhone={onVerifyPhone}
        onUpdateEmail={onUpdateEmail}
        onRefetchUserContact={refetchUserContact}
        onRefetchConsents={refetchConsents}
        SoftOnboardingBanner={SoftOnboardingBanner}
        isValidWalletCard={!!selectedCard}
        geoLocationPermissionStatus={location}
        enableSoftOnboarding={enableSoftOnboarding}
        // @ts-ignore
        brand={env.BRAND}
        onAddPaymentCardFlow={triggerAddPaymentCardFlow as any}>
        <Screens />
      </ProfileScreensContextProvider>
    </ScreenWrapper>
  );
};

export default Render;
