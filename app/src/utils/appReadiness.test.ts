import { AppReadinessManager } from './appReadiness';

// Mock logger
jest.mock('./logger', () => ({
  logger: {
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

describe('AppReadinessManager', () => {
  let manager: AppReadinessManager;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    manager = new AppReadinessManager();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('Basic functionality', () => {
    it('should initialize with all systems not ready', () => {
      const state = manager.getReadinessState();
      expect(state.navigation).toBe(false);
      expect(state.providers).toBe(false);
      expect(state.timeout).toBe(false);
      expect(state.isReady).toBe(false);
    });

    it('should mark navigation as ready', () => {
      manager.markNavigationReady();
      const state = manager.getReadinessState();
      expect(state.navigation).toBe(true);
      expect(state.isReady).toBe(false); // Still need providers
    });

    it('should mark providers as ready', () => {
      manager.markProvidersReady();
      const state = manager.getReadinessState();
      expect(state.providers).toBe(true);
      expect(state.isReady).toBe(false); // Still need navigation
    });

    it('should be ready when both navigation and providers are ready', () => {
      manager.markNavigationReady();
      manager.markProvidersReady();
      const state = manager.getReadinessState();
      expect(state.isReady).toBe(true);
    });
  });

  describe('Callback handling', () => {
    it('should call ready callback when app becomes ready', () => {
      const callback = jest.fn();
      manager.onReady(callback);

      manager.markNavigationReady();
      expect(callback).not.toHaveBeenCalled();

      manager.markProvidersReady();
      expect(callback).toHaveBeenCalledTimes(1);
    });

    it('should call callback immediately if app is already ready', () => {
      const callback = jest.fn();
      
      manager.markNavigationReady();
      manager.markProvidersReady();
      
      manager.onReady(callback);
      expect(callback).toHaveBeenCalledTimes(1);
    });

    it('should call navigation ready callback', () => {
      const callback = jest.fn();
      manager.onNavigationReady(callback);

      manager.markNavigationReady();
      expect(callback).toHaveBeenCalledTimes(1);
    });

    it('should call providers ready callback', () => {
      const callback = jest.fn();
      manager.onProvidersReady(callback);

      manager.markProvidersReady();
      expect(callback).toHaveBeenCalledTimes(1);
    });

    it('should handle callback errors gracefully', () => {
      const errorCallback = jest.fn(() => {
        throw new Error('Test error');
      });
      const normalCallback = jest.fn();

      manager.onReady(errorCallback);
      manager.onReady(normalCallback);

      manager.markNavigationReady();
      manager.markProvidersReady();

      expect(errorCallback).toHaveBeenCalled();
      expect(normalCallback).toHaveBeenCalled();
    });
  });

  describe('Timeout handling', () => {
    it('should become ready after timeout even if systems not ready', () => {
      const callback = jest.fn();
      manager.onReady(callback);

      // Fast-forward past the 2-second timeout
      jest.advanceTimersByTime(2000);

      const state = manager.getReadinessState();
      expect(state.timeout).toBe(true);
      expect(state.isReady).toBe(true);
      expect(callback).toHaveBeenCalledTimes(1);
    });

    it('should clear timeout when app becomes ready normally', () => {
      const callback = jest.fn();
      manager.onReady(callback);

      manager.markNavigationReady();
      manager.markProvidersReady();

      // Fast-forward past timeout
      jest.advanceTimersByTime(2000);

      // Should only be called once (from normal readiness, not timeout)
      expect(callback).toHaveBeenCalledTimes(1);
    });
  });

  describe('Duplicate marking', () => {
    it('should handle duplicate navigation ready calls', () => {
      const callback = jest.fn();
      manager.onNavigationReady(callback);

      manager.markNavigationReady();
      manager.markNavigationReady(); // Duplicate call

      expect(callback).toHaveBeenCalledTimes(1);
    });

    it('should handle duplicate providers ready calls', () => {
      const callback = jest.fn();
      manager.onProvidersReady(callback);

      manager.markProvidersReady();
      manager.markProvidersReady(); // Duplicate call

      expect(callback).toHaveBeenCalledTimes(1);
    });
  });

  describe('Utility methods', () => {
    it('should return provider mount delay', () => {
      const delay = manager.getProviderMountDelay();
      expect(typeof delay).toBe('number');
      expect(delay).toBeGreaterThan(0);
    });

    it('should force ready state', () => {
      manager.forceReady();
      const state = manager.getReadinessState();
      expect(state.isReady).toBe(true);
      expect(state.navigation).toBe(true);
      expect(state.providers).toBe(true);
    });

    it('should reset state', () => {
      manager.markNavigationReady();
      manager.markProvidersReady();
      
      manager.reset();
      
      const state = manager.getReadinessState();
      expect(state.navigation).toBe(false);
      expect(state.providers).toBe(false);
      expect(state.timeout).toBe(false);
      expect(state.isReady).toBe(false);
    });
  });

  describe('Edge cases', () => {
    it('should handle multiple ready callbacks', () => {
      const callback1 = jest.fn();
      const callback2 = jest.fn();
      const callback3 = jest.fn();

      manager.onReady(callback1);
      manager.onReady(callback2);
      manager.onReady(callback3);

      manager.markNavigationReady();
      manager.markProvidersReady();

      expect(callback1).toHaveBeenCalledTimes(1);
      expect(callback2).toHaveBeenCalledTimes(1);
      expect(callback3).toHaveBeenCalledTimes(1);
    });

    it('should clear callbacks after execution', () => {
      const callback = jest.fn();
      manager.onReady(callback);

      manager.markNavigationReady();
      manager.markProvidersReady();

      const state = manager.getReadinessState();
      expect(state.callbackCounts.onReady).toBe(0);
    });
  });
});
