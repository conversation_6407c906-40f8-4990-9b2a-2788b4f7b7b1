import {
  CommonActions,
  createNavigationContainerRef,
} from '@react-navigation/native';
import { BackHandler } from 'react-native';

import { logger } from './logger';
import { appReadinessManager } from './appReadiness';
import { appReadinessManager } from './appReadiness';

export const navigation = createNavigationContainerRef();

// Global app readiness flag
export const isAppReadyRef = { current: false };

// Queue for navigation requests that come before the app is ready
interface QueuedNavigation {
  screenName: string;
  params: object;
  resetStack: boolean;
  timestamp: number;
}

const navigationQueue: QueuedNavigation[] = [];

// Maximum time to wait for app readiness (5 seconds)
const MAX_WAIT_TIME = 5000;

/**
 * Check if navigation is ready (both NavigationContainer and app providers are ready)
 * @returns {boolean} True if navigation is ready, false otherwise
 */
const isNavigationReady = (): boolean => {
  const navReady = navigation.isReady();
  const appReady = isAppReadyRef.current;
  logger.debug(`Navigation ready check - nav: ${navReady}, app: ${appReady}`);
  return navReady && appReady;
};

/**
 * Process queued navigation requests when navigation becomes ready
 */
const processNavigationQueue = () => {
  if (!isNavigationReady()) {
    logger.debug('Navigation not ready, skipping queue processing');
    return;
  }

  logger.debug(
    `Processing navigation queue with ${navigationQueue.length} items`,
  );

  while (navigationQueue.length > 0) {
    const queuedNav = navigationQueue.shift();
    if (queuedNav) {
      logger.debug(`Processing queued navigation: ${queuedNav.screenName}`);
      try {
        executeNavigation(
          queuedNav.screenName,
          queuedNav.params,
          queuedNav.resetStack,
        );
      } catch (error) {
        logger.error(
          `Error processing queued navigation ${queuedNav.screenName}:`,
          error,
        );
      }
    }
  }
};

/**
 * Execute the actual navigation action
 * @param {string} screenName - The screen to navigate to
 * @param {object} params - Navigation parameters
 * @param {boolean} resetStack - Whether to reset the navigation stack
 */
const executeNavigation = (
  screenName: string,
  params: object = {},
  resetStack = false,
) => {
  try {
    if (!navigation.isReady()) {
      throw new Error('Navigation container not ready');
    }

    if (resetStack) {
      navigation.reset({
        index: 0,
        routes: [{ name: screenName, params }],
      });
    } else {
      navigation.dispatch(
        CommonActions.navigate({
          name: screenName,
          params,
        }),
      );
    }
    logger.debug(`Navigation executed: ${screenName}`);
  } catch (error) {
    logger.error(`Navigation error for ${screenName}:`, error);
    throw error; // Re-throw to allow caller to handle
  }
};

/**
 * Wait for navigation to be ready with timeout
 * @returns {Promise<boolean>} Promise that resolves to true if navigation becomes ready, false if timeout
 */
const waitForNavigationReady = (): Promise<boolean> => {
  return new Promise(resolve => {
    if (isNavigationReady()) {
      logger.debug('Navigation already ready, resolving immediately');
      resolve(true);
      return;
    }

    const startTime = Date.now();
    let checkCount = 0;
    const maxChecks = MAX_WAIT_TIME / 100; // Maximum number of checks

    const checkReady = () => {
      checkCount++;

      if (isNavigationReady()) {
        logger.debug(`Navigation became ready after ${checkCount} checks`);
        resolve(true);
        return;
      }

      const elapsed = Date.now() - startTime;
      if (elapsed >= MAX_WAIT_TIME || checkCount >= maxChecks) {
        logger.warn(
          `Navigation ready timeout reached after ${checkCount} checks (${elapsed}ms)`,
        );
        resolve(false);
        return;
      }

      // Check again after a short delay
      setTimeout(checkReady, 100);
    };

    logger.debug('Starting navigation ready check');
    checkReady();
  });
};

/**
 * Navigate to a screen with app readiness checks and queuing
 * This is the main navigation function that should be used throughout the app.
 * It will queue navigation requests if the app is not ready and process them when ready.
 *
 * @param {string} screenName - The screen to navigate to
 * @param {object} params - Navigation parameters (default: {})
 * @param {boolean} resetStack - Whether to reset the navigation stack (default: false)
 * @returns {Promise<void>}
 *
 * @example
 * // Basic navigation
 * await navigate('Home');
 *
 * // Navigation with parameters
 * await navigate('Profile', { userId: '123' });
 *
 * // Navigation with stack reset
 * await navigate('Login', {}, true);
 */
export const navigate = async (
  screenName: string,
  params: object = {},
  resetStack = false,
) => {
  logger.debug(
    `Navigation requested: ${screenName}, ready: ${isNavigationReady()}`,
  );

  // If navigation is ready, execute immediately
  if (isNavigationReady()) {
    try {
      executeNavigation(screenName, params, resetStack);
      return;
    } catch (error) {
      logger.error(`Immediate navigation failed for ${screenName}:`, error);
      // Fall through to queuing
    }
  }

  // Queue the navigation request
  const queuedNav: QueuedNavigation = {
    screenName,
    params,
    resetStack,
    timestamp: Date.now(),
  };

  navigationQueue.push(queuedNav);
  logger.debug(
    `Navigation queued: ${screenName} (queue length: ${navigationQueue.length})`,
  );

  // Try to wait for navigation to be ready
  try {
    const isReady = await waitForNavigationReady();
    if (isReady) {
      logger.debug(`Navigation ready, processing queue for ${screenName}`);
      processNavigationQueue();
    } else {
      logger.warn(
        `Navigation timeout for ${screenName}, will process when ready`,
      );
    }
  } catch (error) {
    logger.error(
      `Error waiting for navigation ready for ${screenName}:`,
      error,
    );
  }
};

/**
 * Synchronous version of navigate for cases where we need immediate navigation
 * Only use this when you're certain the app is ready
 *
 * @param {string} screenName - The screen to navigate to
 * @param {object} params - Navigation parameters (default: {})
 * @param {boolean} resetStack - Whether to reset the navigation stack (default: false)
 */
export const navigateSync = (
  screenName: string,
  params: object = {},
  resetStack = false,
) => {
  if (isNavigationReady()) {
    try {
      executeNavigation(screenName, params, resetStack);
    } catch (error) {
      logger.error(`Sync navigation failed for ${screenName}:`, error);
    }
  } else {
    logger.warn(
      `Navigation not ready for ${screenName}, use navigate() instead`,
    );
  }
};

/**
 * Set app readiness state and process any queued navigation
 * This should be called from App.tsx when all providers are mounted
 *
 * @param {boolean} ready - Whether the app is ready
 */
export const setAppReady = (ready: boolean) => {
  isAppReadyRef.current = ready;
  logger.debug(`App ready state set to: ${ready}`);

  if (ready) {
    // Process any queued navigation requests immediately when app becomes ready
    logger.debug('Processing navigation queue after app ready');
    processNavigationQueue();

    // Add a second attempt after a short delay to handle edge cases
    setTimeout(() => {
      if (navigationQueue.length > 0) {
        logger.debug('Second attempt to process navigation queue');
        processNavigationQueue();
      }
    }, 500);
  }
};

/**
 * Force process navigation queue (useful for debugging or manual intervention)
 */
export const forceProcessNavigationQueue = () => {
  logger.debug('Force processing navigation queue');
  processNavigationQueue();
};

/**
 * Get current navigation queue status for debugging
 * @returns {object} Status object with navigation readiness and queue information
 */
export const getNavigationQueueStatus = () => {
  return {
    isNavigationReady: isNavigationReady(),
    isAppReady: isAppReadyRef.current,
    queueLength: navigationQueue.length,
    queuedItems: navigationQueue.map(item => ({
      screen: item.screenName,
      timestamp: item.timestamp,
    })),
  };
};

/**
 * Debug utility to log navigation status
 */
export const logNavigationStatus = () => {
  const status = getNavigationQueueStatus();
  logger.debug('Navigation Status:', JSON.stringify(status, null, 2));
};

/**
 * Clear navigation queue (useful for testing or error recovery)
 * @returns {number} Number of cleared navigation requests
 */
export const clearNavigationQueue = () => {
  const clearedCount = navigationQueue.length;
  navigationQueue.length = 0;
  logger.debug(`Cleared ${clearedCount} queued navigation requests`);
  return clearedCount;
};

/**
 * Test navigation function to verify the system is working
 * This can be called from the debug console or a test screen
 * @param {string} screenName - Screen to navigate to
 * @returns {Promise<void>}
 */
export const testNavigation = async (screenName: string = 'Tabs') => {
  logger.debug('Testing navigation system...');
  logNavigationStatus();

  try {
    await navigate(screenName, { test: true });
    logger.debug('Test navigation completed successfully');
  } catch (error) {
    logger.error('Test navigation failed:', error);
  }

  // Log status after test
  setTimeout(() => {
    logNavigationStatus();
  }, 500);
};

// Returns a boolean based on if the user can go back further than the current screen
export const canGoBack = () => navigation.canGoBack();

// Prevent hardware back if back options not available
// This prevents the app closing in Android
const handleHardwareBack = () => !canGoBack();

// Handles applying hardware back action event listener
export const applyBackHandleListener = () =>
  BackHandler.addEventListener('hardwareBackPress', handleHardwareBack);

// Handles removing hardware back action event listener
export const removeBackHandleListener = () =>
  BackHandler.removeEventListener('hardwareBackPress', handleHardwareBack);

export type RootStackParamList = {};
