import { logger } from './logger';

interface ReadinessState {
  navigation: boolean;
  providers: boolean;
  timeout: boolean;
}

interface ReadinessCallbacks {
  onReady: (() => void)[];
  onNavigationReady: (() => void)[];
  onProvidersReady: (() => void)[];
}

/**
 * Manages app readiness state without relying on arbitrary timeouts.
 * Tracks when critical systems (navigation, providers) are ready and
 * provides a fallback timeout for edge cases.
 */
class AppReadinessManager {
  private state: ReadinessState = {
    navigation: false,
    providers: false,
    timeout: false,
  };
  
  private timeoutId: NodeJS.Timeout | null = null;
  private callbacks: ReadinessCallbacks = {
    onReady: [],
    onNavigationReady: [],
    onProvidersReady: [],
  };

  private isReady = false;
  private readonly FALLBACK_TIMEOUT = 2000; // 2 seconds fallback
  private readonly PROVIDER_MOUNT_DELAY = 100; // Minimal delay for provider mounting

  constructor() {
    this.initializeFallbackTimeout();
  }

  /**
   * Initialize the fallback timeout that ensures app becomes ready
   * even if some systems don't report readiness
   */
  private initializeFallbackTimeout() {
    this.timeoutId = setTimeout(() => {
      logger.warn(
        'App readiness timeout reached after 2s, proceeding anyway',
        {
          state: this.state,
          readyCallbacksCount: this.callbacks.onReady.length,
        }
      );
      this.state.timeout = true;
      this.checkAndNotify();
    }, this.FALLBACK_TIMEOUT);
  }

  /**
   * Mark navigation container as ready
   */
  markNavigationReady() {
    if (this.state.navigation) {
      logger.debug('Navigation already marked as ready, skipping');
      return;
    }

    this.state.navigation = true;
    logger.debug('Navigation container marked as ready');
    
    // Notify navigation-specific callbacks
    this.callbacks.onNavigationReady.forEach(callback => {
      try {
        callback();
      } catch (error) {
        logger.error('Error in navigation ready callback:', error);
      }
    });
    this.callbacks.onNavigationReady = [];

    this.checkAndNotify();
  }

  /**
   * Mark providers as ready (called after provider mounting)
   */
  markProvidersReady() {
    if (this.state.providers) {
      logger.debug('Providers already marked as ready, skipping');
      return;
    }

    this.state.providers = true;
    logger.debug('Providers marked as ready');
    
    // Notify provider-specific callbacks
    this.callbacks.onProvidersReady.forEach(callback => {
      try {
        callback();
      } catch (error) {
        logger.error('Error in providers ready callback:', error);
      }
    });
    this.callbacks.onProvidersReady = [];

    this.checkAndNotify();
  }

  /**
   * Check if app is ready and notify callbacks
   */
  private checkAndNotify() {
    const wasReady = this.isReady;
    this.isReady = (this.state.navigation && this.state.providers) || this.state.timeout;
    
    if (this.isReady && !wasReady) {
      this.cleanup();
      this.notifyReadyCallbacks();
    }
  }

  /**
   * Clean up timeout and resources
   */
  private cleanup() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  /**
   * Notify all ready callbacks
   */
  private notifyReadyCallbacks() {
    const readinessMethod = this.state.timeout ? 'timeout' : 'normal';
    logger.debug(`App is fully ready (via ${readinessMethod})`, {
      state: this.state,
      callbackCount: this.callbacks.onReady.length,
    });

    this.callbacks.onReady.forEach(callback => {
      try {
        callback();
      } catch (error) {
        logger.error('Error in app ready callback:', error);
      }
    });
    this.callbacks.onReady = [];
  }

  /**
   * Register callback for when app becomes ready
   */
  onReady(callback: () => void) {
    if (this.isReady) {
      // App is already ready, execute callback immediately
      logger.debug('App already ready, executing callback immediately');
      try {
        callback();
      } catch (error) {
        logger.error('Error in immediate ready callback:', error);
      }
      return;
    }

    this.callbacks.onReady.push(callback);
    logger.debug(`Registered ready callback (total: ${this.callbacks.onReady.length})`);
  }

  /**
   * Register callback for when navigation becomes ready
   */
  onNavigationReady(callback: () => void) {
    if (this.state.navigation) {
      try {
        callback();
      } catch (error) {
        logger.error('Error in immediate navigation ready callback:', error);
      }
      return;
    }

    this.callbacks.onNavigationReady.push(callback);
  }

  /**
   * Register callback for when providers become ready
   */
  onProvidersReady(callback: () => void) {
    if (this.state.providers) {
      try {
        callback();
      } catch (error) {
        logger.error('Error in immediate providers ready callback:', error);
      }
      return;
    }

    this.callbacks.onProvidersReady.push(callback);
  }

  /**
   * Get current readiness state (for debugging)
   */
  getReadinessState() {
    return {
      ...this.state,
      isReady: this.isReady,
      callbackCounts: {
        onReady: this.callbacks.onReady.length,
        onNavigationReady: this.callbacks.onNavigationReady.length,
        onProvidersReady: this.callbacks.onProvidersReady.length,
      },
    };
  }

  /**
   * Get the minimal delay used for provider mounting
   */
  getProviderMountDelay() {
    return this.PROVIDER_MOUNT_DELAY;
  }

  /**
   * Force mark app as ready (for testing or emergency scenarios)
   */
  forceReady() {
    logger.warn('Force marking app as ready');
    this.state.navigation = true;
    this.state.providers = true;
    this.checkAndNotify();
  }

  /**
   * Reset the readiness manager (for testing)
   */
  reset() {
    this.cleanup();
    this.state = {
      navigation: false,
      providers: false,
      timeout: false,
    };
    this.callbacks = {
      onReady: [],
      onNavigationReady: [],
      onProvidersReady: [],
    };
    this.isReady = false;
    this.initializeFallbackTimeout();
    logger.debug('App readiness manager reset');
  }
}

// Export singleton instance
export const appReadinessManager = new AppReadinessManager();

// Export for testing
export { AppReadinessManager };
